#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试2021年数据解析问题
"""

import pandas as pd
import re

def detailed_analysis_2021():
    """详细分析2021年数据结构"""
    print("=== 详细分析2021年数据结构 ===")
    
    file_2021 = "[八爪鱼爬取]最全！31省区市2021年高考分数线完整版.xlsx"
    df_raw = pd.read_excel(file_2021)
    
    print(f"数据形状: {df_raw.shape}")
    print(f"列名: {df_raw.columns.tolist()}")
    
    print("\n=== 所有原始数据内容 ===")
    for index, row in df_raw.iterrows():
        text = str(row.iloc[0]).strip()
        print(f"行{index:3d}: {text}")
    
    print("\n=== 寻找省份模式 ===")
    
    # 中国31个省市自治区完整列表
    all_provinces = [
        '北京', '天津', '河北', '山西', '内蒙古',
        '辽宁', '吉林', '黑龙江',
        '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东',
        '河南', '湖北', '湖南', '广东', '广西', '海南',
        '重庆', '四川', '贵州', '云南', '西藏',
        '陕西', '甘肃', '青海', '宁夏', '新疆'
    ]
    
    found_provinces = []
    province_lines = []
    
    for index, row in df_raw.iterrows():
        text = str(row.iloc[0]).strip()
        
        if text == 'nan' or text == '':
            continue
            
        # 检查是否包含省份名称
        for province in all_provinces:
            if province in text:
                # 进一步验证这是省份标题行
                if ('：' in text or '，' in text or text.startswith(province) or text.endswith(province)):
                    if province not in found_provinces:
                        found_provinces.append(province)
                        province_lines.append((index, province, text))
                        print(f"行{index:3d}: 发现省份 {province} -> {text}")
                    break
    
    print(f"\n总共识别到 {len(found_provinces)} 个省份:")
    for i, province in enumerate(found_provinces, 1):
        print(f"{i:2d}. {province}")
    
    print(f"\n缺失的省份:")
    missing_provinces = [p for p in all_provinces if p not in found_provinces]
    for province in missing_provinces:
        print(f"- {province}")
    
    return df_raw, province_lines, found_provinces, missing_provinces

def find_missing_provinces_manually():
    """手动查找缺失省份的数据"""
    print("\n=== 手动查找缺失省份 ===")
    
    file_2021 = "[八爪鱼爬取]最全！31省区市2021年高考分数线完整版.xlsx"
    df_raw = pd.read_excel(file_2021)
    
    # 缺失的省份（根据上面的分析结果）
    missing_provinces = [
        '北京', '天津', '河北', '山西', '内蒙古',
        '辽宁', '吉林', '黑龙江', '上海', '江苏',
        '浙江', '安徽', '福建', '江西', '山东',
        '河南', '湖北', '湖南', '广东', '广西',
        '海南', '重庆', '四川', '贵州', '云南',
        '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆'
    ]
    
    print("在所有文本中搜索缺失省份的关键词:")
    
    for province in missing_provinces[:10]:  # 先检查前10个
        print(f"\n--- 搜索 {province} ---")
        found_lines = []
        
        for index, row in df_raw.iterrows():
            text = str(row.iloc[0]).strip()
            if province in text:
                found_lines.append((index, text))
        
        if found_lines:
            print(f"找到 {len(found_lines)} 行包含 '{province}':")
            for line_num, text in found_lines:
                print(f"  行{line_num}: {text}")
        else:
            print(f"未找到包含 '{province}' 的行")

def analyze_text_patterns():
    """分析文本模式，寻找更好的解析方法"""
    print("\n=== 分析文本模式 ===")
    
    file_2021 = "[八爪鱼爬取]最全！31省区市2021年高考分数线完整版.xlsx"
    df_raw = pd.read_excel(file_2021)
    
    # 分析所有包含分数的行
    score_lines = []
    for index, row in df_raw.iterrows():
        text = str(row.iloc[0]).strip()
        if re.search(r'\d+分', text):
            score_lines.append((index, text))
    
    print(f"找到 {len(score_lines)} 行包含分数信息:")
    for line_num, text in score_lines[:20]:  # 显示前20行
        print(f"行{line_num:3d}: {text}")
    
    # 分析可能的省份标识模式
    print(f"\n=== 分析省份标识模式 ===")
    
    # 查找可能的分隔符或标识符
    separators = ['：', '，', '。', '、', '；', '|', '-', '=']
    
    for sep in separators:
        lines_with_sep = []
        for index, row in df_raw.iterrows():
            text = str(row.iloc[0]).strip()
            if sep in text and not re.search(r'\d+分', text):  # 不包含分数的分隔符行
                lines_with_sep.append((index, text))
        
        if lines_with_sep:
            print(f"\n包含 '{sep}' 的非分数行 ({len(lines_with_sep)}行):")
            for line_num, text in lines_with_sep[:5]:  # 显示前5行
                print(f"  行{line_num}: {text}")

def main():
    """主函数"""
    print("调试2021年数据解析问题")
    print("=" * 50)
    
    # 1. 详细分析数据结构
    df_raw, province_lines, found_provinces, missing_provinces = detailed_analysis_2021()
    
    # 2. 手动查找缺失省份
    find_missing_provinces_manually()
    
    # 3. 分析文本模式
    analyze_text_patterns()
    
    print(f"\n=== 总结 ===")
    print(f"应有省份数: 31个")
    print(f"已识别省份数: {len(found_provinces)}个")
    print(f"缺失省份数: {len(missing_provinces)}个")

if __name__ == "__main__":
    main()
