import pandas as pd
import re

# 读取2024年数据
file_2024 = "[八爪鱼爬取]全国2024年高考录取分数线一览表汇总（含各省本科线、专科线）-高考100.xlsx"
df_raw = pd.read_excel(file_2024)

print(f"数据形状: {df_raw.shape}")
print(f"列名: {df_raw.columns.tolist()}")

print("\n前20行数据:")
for i in range(min(20, len(df_raw))):
    text = str(df_raw.iloc[i, 0]).strip()
    print(f"{i:2d}: {text}")

print("\n查找省份模式:")
provinces = []
for i, row in df_raw.iterrows():
    text = str(row.iloc[0]).strip()
    if re.match(r'^\d+\.', text):
        province = re.sub(r'^\d+\.', '', text).strip()
        provinces.append(province)
        print(f"行{i}: {province}")

print(f"\n总共找到 {len(provinces)} 个省份")
