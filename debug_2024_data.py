#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试2024年数据解析问题
"""

import pandas as pd
import re

def analyze_2024_data_structure():
    """详细分析2024年数据结构"""
    print("=== 详细分析2024年数据结构 ===")
    
    file_2024 = "[八爪鱼爬取]全国2024年高考录取分数线一览表汇总（含各省本科线、专科线）-高考100.xlsx"
    df_raw = pd.read_excel(file_2024)
    
    print(f"原始数据形状: {df_raw.shape}")
    print(f"列名: {df_raw.columns.tolist()}")
    
    print("\n=== 所有原始数据内容 ===")
    for index, row in df_raw.iterrows():
        text = str(row.iloc[0]).strip()
        print(f"行{index:3d}: {text}")
    
    print("\n=== 识别省份名称 ===")
    provinces = []
    for index, row in df_raw.iterrows():
        text = str(row.iloc[0]).strip()
        
        # 检查是否是省份名称（以数字开头）
        if re.match(r'^\d+\.', text):
            province = re.sub(r'^\d+\.', '', text).strip()
            provinces.append((index, province))
            print(f"行{index:3d}: 发现省份 -> {province}")
    
    print(f"\n总共识别到 {len(provinces)} 个省份:")
    for i, (index, province) in enumerate(provinces, 1):
        print(f"{i:2d}. {province}")
    
    return df_raw, provinces

def analyze_score_patterns():
    """分析分数线模式"""
    print("\n=== 分析分数线模式 ===")
    
    file_2024 = "[八爪鱼爬取]全国2024年高考录取分数线一览表汇总（含各省本科线、专科线）-高考100.xlsx"
    df_raw = pd.read_excel(file_2024)
    
    current_province = None
    score_patterns = []
    
    for index, row in df_raw.iterrows():
        text = str(row.iloc[0]).strip()
        
        # 检查是否是省份名称
        if re.match(r'^\d+\.', text):
            current_province = re.sub(r'^\d+\.', '', text).strip()
            continue
        
        if current_province and '：' in text:
            score_patterns.append({
                'province': current_province,
                'line': index,
                'text': text,
                'has_scores': bool(re.findall(r'\d+分', text)),
                'scores': re.findall(r'(\d+)分', text),
                'has_batch': any(batch in text for batch in ['本科一批', '本科二批', '专科批', '本科批'])
            })
    
    print(f"找到 {len(score_patterns)} 条分数线记录")
    
    # 按省份分组统计
    province_stats = {}
    for pattern in score_patterns:
        province = pattern['province']
        if province not in province_stats:
            province_stats[province] = []
        province_stats[province].append(pattern)
    
    print(f"\n各省份分数线记录数:")
    for province, records in province_stats.items():
        valid_records = [r for r in records if r['has_scores'] and r['has_batch']]
        print(f"{province}: {len(records)}条记录, {len(valid_records)}条有效")
        
        # 显示前几条记录
        for i, record in enumerate(records[:3]):
            print(f"  {i+1}. {record['text']}")
            if record['has_scores']:
                print(f"     分数: {record['scores']}")
    
    return score_patterns

def improved_parse_2024_data():
    """改进的2024年数据解析"""
    print("\n=== 改进的2024年数据解析 ===")
    
    file_2024 = "[八爪鱼爬取]全国2024年高考录取分数线一览表汇总（含各省本科线、专科线）-高考100.xlsx"
    df_raw = pd.read_excel(file_2024)
    
    data_2024 = []
    current_province = None
    
    for index, row in df_raw.iterrows():
        text = str(row.iloc[0]).strip()
        
        # 检查是否是省份名称（以数字开头）
        if re.match(r'^\d+\.', text):
            current_province = re.sub(r'^\d+\.', '', text).strip()
            print(f"处理省份: {current_province}")
            continue
        
        if current_province and text and text != 'nan':
            # 更宽松的分数线识别
            scores = re.findall(r'(\d+)分', text)
            
            if scores:  # 如果找到分数
                # 识别批次
                batch = None
                if '本科一批' in text or '一本' in text:
                    batch = '本科一批'
                elif '本科二批' in text or '二本' in text:
                    batch = '本科二批'
                elif '专科批' in text or '专科' in text:
                    batch = '专科批'
                elif '本科批' in text:
                    batch = '本科批'
                
                if batch:  # 如果识别到批次
                    # 识别科目类别
                    if ('理科' in text or '理工' in text) and ('文科' in text or '文史' in text):
                        # 文理科都有
                        if len(scores) >= 2:
                            # 理科通常在前
                            data_2024.append({
                                '地区': current_province,
                                '年份': 2024,
                                '考生类别': '理科',
                                '录取批次': batch,
                                '录取分数线': int(scores[0])
                            })
                            data_2024.append({
                                '地区': current_province,
                                '年份': 2024,
                                '考生类别': '文科',
                                '录取批次': batch,
                                '录取分数线': int(scores[1])
                            })
                    elif '理科' in text or '理工' in text:
                        # 只有理科
                        data_2024.append({
                            '地区': current_province,
                            '年份': 2024,
                            '考生类别': '理科',
                            '录取批次': batch,
                            '录取分数线': int(scores[0])
                        })
                    elif '文科' in text or '文史' in text:
                        # 只有文科
                        data_2024.append({
                            '地区': current_province,
                            '年份': 2024,
                            '考生类别': '文科',
                            '录取批次': batch,
                            '录取分数线': int(scores[0])
                        })
                    elif '综合' in text or '不分文理' in text:
                        # 综合类
                        data_2024.append({
                            '地区': current_province,
                            '年份': 2024,
                            '考生类别': '综合',
                            '录取批次': batch,
                            '录取分数线': int(scores[0])
                        })
                    elif len(scores) == 1:
                        # 如果只有一个分数且没有明确科目，可能是综合
                        data_2024.append({
                            '地区': current_province,
                            '年份': 2024,
                            '考生类别': '综合',
                            '录取批次': batch,
                            '录取分数线': int(scores[0])
                        })
    
    df_2024 = pd.DataFrame(data_2024)
    print(f"\n改进解析结果:")
    print(f"数据形状: {df_2024.shape}")
    print(f"省份数量: {df_2024['地区'].nunique()}")
    print(f"省份列表: {sorted(df_2024['地区'].unique())}")
    
    # 按省份统计
    province_counts = df_2024['地区'].value_counts()
    print(f"\n各省份记录数:")
    for province, count in province_counts.items():
        print(f"{province}: {count}条")
    
    return df_2024

def main():
    """主函数"""
    print("调试2024年数据解析问题")
    print("=" * 50)
    
    # 1. 分析数据结构
    df_raw, provinces = analyze_2024_data_structure()
    
    # 2. 分析分数线模式
    score_patterns = analyze_score_patterns()
    
    # 3. 改进解析
    df_improved = improved_parse_2024_data()
    
    print(f"\n=== 总结 ===")
    print(f"原始数据行数: {len(df_raw)}")
    print(f"识别到的省份数: {len(provinces)}")
    print(f"改进解析后的记录数: {len(df_improved)}")
    print(f"改进解析后的省份数: {df_improved['地区'].nunique()}")

if __name__ == "__main__":
    main()
