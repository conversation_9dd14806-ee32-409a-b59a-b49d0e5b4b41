#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三年高考分数线数据规范化脚本 (2021-2024)
作者：数据分析小组
日期：2024年
"""

import pandas as pd
import numpy as np
import re
import os
from datetime import datetime

def clean_2023_data():
    """清理和规范化2023年数据"""
    print("=== 处理2023年数据 ===")
    
    file_2023 = "[八爪鱼爬取]2023年全国各地高考分数线公布汇总：一本、二本、专科.xlsx"
    df_2023 = pd.read_excel(file_2023)
    
    print(f"原始数据形状: {df_2023.shape}")
    
    # 删除重复的表头行
    df_2023 = df_2023[df_2023['地区'] != '地区'].copy()
    
    # 数据类型转换
    df_2023['年份'] = pd.to_numeric(df_2023['年份'], errors='coerce')
    df_2023['录取分数线'] = pd.to_numeric(df_2023['录取分数线'], errors='coerce')
    
    # 删除包含NaN的行
    df_2023 = df_2023.dropna()
    
    # 标准化字段
    df_2023['地区'] = df_2023['地区'].str.strip()
    df_2023['考生类别'] = df_2023['考生类别'].str.strip()
    df_2023['录取批次'] = df_2023['录取批次'].str.strip()
    
    # 添加辅助字段
    df_2023['数据来源'] = '八爪鱼爬取'
    df_2023['更新时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    print(f"清理后数据形状: {df_2023.shape}")
    return df_2023

def parse_2021_data():
    """解析和规范化2021年数据"""
    print("\n=== 处理2021年数据 ===")
    
    file_2021 = "[八爪鱼爬取]最全！31省区市2021年高考分数线完整版.xlsx"
    df_raw = pd.read_excel(file_2021)
    
    print(f"原始数据形状: {df_raw.shape}")
    
    data_2021 = []
    current_province = None
    
    for index, row in df_raw.iterrows():
        text = str(row.iloc[0]).strip()
        
        if text == 'nan' or text == '' or '综合消息' in text or '各省份录取分数线如下' in text:
            continue
        
        # 识别省份名称模式
        province_patterns = [
            r'^([^：]+)：',  # 省份：内容
            r'^([^，]+)，',  # 省份，内容
        ]
        
        province_found = False
        for pattern in province_patterns:
            match = re.match(pattern, text)
            if match:
                potential_province = match.group(1).strip()
                # 检查是否是省份名称
                if any(keyword in potential_province for keyword in ['省', '市', '区', '宁夏', '西藏', '新疆', '内蒙古', '广西', '香港', '澳门']):
                    current_province = potential_province
                    province_found = True
                    break
        
        if province_found:
            continue
        
        # 解析分数线信息
        if current_province and ('一本' in text or '二本' in text or '专科' in text or '本科' in text):
            # 提取分数
            scores = re.findall(r'(\d+)分', text)
            
            if scores:
                # 解析批次和科目
                if '一本' in text:
                    batch = '本科一批'
                elif '二本' in text:
                    batch = '本科二批'
                elif '专科' in text:
                    batch = '专科批'
                elif '本科' in text:
                    batch = '本科批'
                else:
                    continue
                
                # 解析科目
                if '文科' in text and '理科' in text:
                    # 通常文科在前，理科在后
                    if len(scores) >= 2:
                        # 查找文科和理科分数的位置
                        text_parts = text.split()
                        wenke_score = None
                        like_score = None
                        
                        for part in text_parts:
                            if '文科' in part or '文史' in part:
                                score_match = re.search(r'(\d+)分?', part)
                                if score_match:
                                    wenke_score = int(score_match.group(1))
                            elif '理科' in part or '理工' in part:
                                score_match = re.search(r'(\d+)分?', part)
                                if score_match:
                                    like_score = int(score_match.group(1))
                        
                        if wenke_score:
                            data_2021.append({
                                '地区': current_province,
                                '年份': 2021,
                                '考生类别': '文科',
                                '录取批次': batch,
                                '录取分数线': wenke_score,
                                '数据来源': '八爪鱼爬取',
                                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })
                        
                        if like_score:
                            data_2021.append({
                                '地区': current_province,
                                '年份': 2021,
                                '考生类别': '理科',
                                '录取批次': batch,
                                '录取分数线': like_score,
                                '数据来源': '八爪鱼爬取',
                                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })
                elif '文科' in text or '文史' in text:
                    if scores:
                        data_2021.append({
                            '地区': current_province,
                            '年份': 2021,
                            '考生类别': '文科',
                            '录取批次': batch,
                            '录取分数线': int(scores[0]),
                            '数据来源': '八爪鱼爬取',
                            '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                elif '理科' in text or '理工' in text:
                    if scores:
                        data_2021.append({
                            '地区': current_province,
                            '年份': 2021,
                            '考生类别': '理科',
                            '录取批次': batch,
                            '录取分数线': int(scores[0]),
                            '数据来源': '八爪鱼爬取',
                            '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                elif '综合' in text or '不分文理' in text:
                    if scores:
                        data_2021.append({
                            '地区': current_province,
                            '年份': 2021,
                            '考生类别': '综合',
                            '录取批次': batch,
                            '录取分数线': int(scores[0]),
                            '数据来源': '八爪鱼爬取',
                            '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
    
    df_2021 = pd.DataFrame(data_2021)
    print(f"解析后数据形状: {df_2021.shape}")
    print(f"解析后省份数量: {df_2021['地区'].nunique()}")
    print(f"解析后省份列表: {sorted(df_2021['地区'].unique())}")
    
    return df_2021

def parse_and_clean_2024_data():
    """解析和规范化2024年数据 - 改进版"""
    print("\n=== 处理2024年数据 ===")
    
    file_2024 = "[八爪鱼爬取]全国2024年高考录取分数线一览表汇总（含各省本科线、专科线）-高考100.xlsx"
    df_raw = pd.read_excel(file_2024)
    
    print(f"原始数据形状: {df_raw.shape}")
    
    data_2024 = []
    current_province = "宁夏"  # 第一个省份是宁夏，但没有标号
    
    for index, row in df_raw.iterrows():
        text = str(row.iloc[0]).strip()
        
        # 检查是否是省份名称（以数字开头）
        if re.match(r'^\d+\.', text):
            current_province = re.sub(r'^\d+\.', '', text).strip()
            continue
        
        if current_province and text and text != 'nan':
            # 提取分数
            scores = re.findall(r'(\d+)分', text)
            
            if scores:  # 如果找到分数
                # 识别批次 - 更全面的批次识别
                batch = None
                if '本科一批' in text:
                    batch = '本科一批'
                elif '本科二批' in text:
                    batch = '本科二批'
                elif '专科批' in text:
                    batch = '专科批'
                elif '本科批' in text:
                    batch = '本科批'
                elif '特控批' in text:
                    batch = '特殊类型招生控制线'
                elif '特控线' in text:
                    batch = '特殊类型招生控制线'
                elif '本科控制线' in text:
                    batch = '本科批'
                elif '一段线' in text or '第一段' in text:
                    batch = '本科一批'
                elif '二段线' in text or '第二段' in text:
                    batch = '本科二批'
                elif '本科一段' in text:
                    batch = '本科一批'
                elif '本科二段' in text:
                    batch = '本科二批'
                
                if batch:  # 如果识别到批次
                    # 识别科目类别 - 更全面的科目识别
                    if ('理科' in text or '理工' in text) and ('文科' in text or '文史' in text):
                        # 文理科都有
                        if len(scores) >= 2:
                            data_2024.append({
                                '地区': current_province,
                                '年份': 2024,
                                '考生类别': '理科',
                                '录取批次': batch,
                                '录取分数线': int(scores[0]),
                                '数据来源': '八爪鱼爬取',
                                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })
                            data_2024.append({
                                '地区': current_province,
                                '年份': 2024,
                                '考生类别': '文科',
                                '录取批次': batch,
                                '录取分数线': int(scores[1]),
                                '数据来源': '八爪鱼爬取',
                                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })
                    elif ('物理类' in text) and ('历史类' in text):
                        # 物理类和历史类（新高考）
                        if len(scores) >= 2:
                            data_2024.append({
                                '地区': current_province,
                                '年份': 2024,
                                '考生类别': '物理类',
                                '录取批次': batch,
                                '录取分数线': int(scores[0]),
                                '数据来源': '八爪鱼爬取',
                                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })
                            data_2024.append({
                                '地区': current_province,
                                '年份': 2024,
                                '考生类别': '历史类',
                                '录取批次': batch,
                                '录取分数线': int(scores[1]),
                                '数据来源': '八爪鱼爬取',
                                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })
                    elif '理科' in text or '理工' in text:
                        # 只有理科
                        data_2024.append({
                            '地区': current_province,
                            '年份': 2024,
                            '考生类别': '理科',
                            '录取批次': batch,
                            '录取分数线': int(scores[0]),
                            '数据来源': '八爪鱼爬取',
                            '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                    elif '文科' in text or '文史' in text:
                        # 只有文科
                        data_2024.append({
                            '地区': current_province,
                            '年份': 2024,
                            '考生类别': '文科',
                            '录取批次': batch,
                            '录取分数线': int(scores[0]),
                            '数据来源': '八爪鱼爬取',
                            '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                    elif '物理类' in text:
                        # 只有物理类
                        data_2024.append({
                            '地区': current_province,
                            '年份': 2024,
                            '考生类别': '物理类',
                            '录取批次': batch,
                            '录取分数线': int(scores[0]),
                            '数据来源': '八爪鱼爬取',
                            '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                    elif '历史类' in text:
                        # 只有历史类
                        data_2024.append({
                            '地区': current_province,
                            '年份': 2024,
                            '考生类别': '历史类',
                            '录取批次': batch,
                            '录取分数线': int(scores[0]),
                            '数据来源': '八爪鱼爬取',
                            '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                    elif '综合' in text or '不分文理' in text:
                        # 综合类
                        data_2024.append({
                            '地区': current_province,
                            '年份': 2024,
                            '考生类别': '综合',
                            '录取批次': batch,
                            '录取分数线': int(scores[0]),
                            '数据来源': '八爪鱼爬取',
                            '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                    else:
                        # 如果没有明确科目标识，根据省份和批次推断
                        if len(scores) == 1:
                            # 可能是综合类或者单一分数线
                            data_2024.append({
                                '地区': current_province,
                                '年份': 2024,
                                '考生类别': '综合',
                                '录取批次': batch,
                                '录取分数线': int(scores[0]),
                                '数据来源': '八爪鱼爬取',
                                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })
                        elif len(scores) >= 2:
                            # 可能是文理科，按顺序分配
                            data_2024.append({
                                '地区': current_province,
                                '年份': 2024,
                                '考生类别': '理科',
                                '录取批次': batch,
                                '录取分数线': int(scores[0]),
                                '数据来源': '八爪鱼爬取',
                                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })
                            data_2024.append({
                                '地区': current_province,
                                '年份': 2024,
                                '考生类别': '文科',
                                '录取批次': batch,
                                '录取分数线': int(scores[1]),
                                '数据来源': '八爪鱼爬取',
                                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })
    
    df_2024 = pd.DataFrame(data_2024)
    print(f"解析后数据形状: {df_2024.shape}")
    print(f"解析后省份数量: {df_2024['地区'].nunique()}")
    
    return df_2024

def create_standardized_datasets_3years(df_2021, df_2023, df_2024):
    """创建三年标准化数据集"""
    print("\n=== 创建三年标准化数据集 ===")

    # 确保列名一致
    columns_order = ['地区', '年份', '考生类别', '录取批次', '录取分数线', '数据来源', '更新时间']

    # 重新排列列顺序
    df_2021 = df_2021[columns_order]
    df_2023 = df_2023[columns_order]
    df_2024 = df_2024[columns_order]

    # 合并数据
    df_combined = pd.concat([df_2021, df_2023, df_2024], ignore_index=True)

    # 添加辅助分析字段
    df_combined['分数线等级'] = pd.cut(df_combined['录取分数线'],
                                bins=[0, 200, 300, 400, 500, 600, 800],
                                labels=['极低', '低', '中等', '较高', '高', '极高'])

    # 添加地区分类（简化版）
    east_regions = ['北京', '天津', '河北', '山东', '江苏', '上海', '浙江', '福建', '广东', '海南']
    central_regions = ['山西', '安徽', '江西', '河南', '湖北', '湖南']
    west_regions = ['内蒙古', '广西', '重庆', '四川', '贵州', '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆']
    northeast_regions = ['辽宁', '吉林', '黑龙江']

    def classify_region(region):
        if region in east_regions:
            return '东部'
        elif region in central_regions:
            return '中部'
        elif region in west_regions:
            return '西部'
        elif region in northeast_regions:
            return '东北'
        else:
            return '其他'

    df_combined['地区分类'] = df_combined['地区'].apply(classify_region)

    print(f"合并后数据形状: {df_combined.shape}")
    print("三年数据概览:")
    print(df_combined.info())

    return df_combined, df_2021, df_2023, df_2024

def export_for_finebi_3years(df_combined, df_2021, df_2023, df_2024):
    """导出适用于FineBI的三年数据文件"""
    print("\n=== 导出FineBI三年数据文件 ===")

    # 创建输出目录
    output_dir = 'finebi_data_3years'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 1. 导出合并数据集（主要用于综合分析）
    combined_file = os.path.join(output_dir, '高考分数线合并数据_2021-2024.xlsx')
    with pd.ExcelWriter(combined_file, engine='openpyxl') as writer:
        df_combined.to_excel(writer, sheet_name='合并数据', index=False)

        # 添加数据字典
        data_dict = pd.DataFrame({
            '字段名': ['地区', '年份', '考生类别', '录取批次', '录取分数线', '数据来源', '更新时间', '分数线等级', '地区分类'],
            '字段类型': ['文本', '数值', '文本', '文本', '数值', '文本', '日期时间', '文本', '文本'],
            '字段说明': [
                '省份/直辖市/自治区名称',
                '高考年份',
                '考生类别（文科/理科/综合/历史类/物理类）',
                '录取批次（本科一批/本科二批/专科批等）',
                '录取分数线（分）',
                '数据来源标识',
                '数据更新时间',
                '分数线等级分类（极低/低/中等/较高/高/极高）',
                '地区分类（东部/中部/西部/东北/其他）'
            ],
            '示例值': [
                '北京',
                '2023',
                '综合',
                '本科批',
                '448',
                '八爪鱼爬取',
                '2024-01-01 12:00:00',
                '较高',
                '东部'
            ]
        })
        data_dict.to_excel(writer, sheet_name='数据字典', index=False)

    print(f"三年合并数据已导出: {combined_file}")

    # 2. 导出分年度数据
    df_2021_file = os.path.join(output_dir, '高考分数线_2021年.xlsx')
    df_2021.to_excel(df_2021_file, index=False)
    print(f"2021年数据已导出: {df_2021_file}")

    df_2023_file = os.path.join(output_dir, '高考分数线_2023年.xlsx')
    df_2023.to_excel(df_2023_file, index=False)
    print(f"2023年数据已导出: {df_2023_file}")

    df_2024_file = os.path.join(output_dir, '高考分数线_2024年.xlsx')
    df_2024.to_excel(df_2024_file, index=False)
    print(f"2024年数据已导出: {df_2024_file}")

    # 3. 创建透视表数据（便于FineBI快速分析）
    pivot_file = os.path.join(output_dir, '高考分数线透视分析表_三年.xlsx')
    with pd.ExcelWriter(pivot_file, engine='openpyxl') as writer:

        # 按地区年份汇总
        pivot_region_year = df_combined.pivot_table(
            values='录取分数线',
            index=['地区', '地区分类'],
            columns='年份',
            aggfunc=['mean', 'min', 'max', 'count'],
            fill_value=0
        ).round(1)
        pivot_region_year.to_excel(writer, sheet_name='地区年份汇总')

        # 按批次类别汇总
        pivot_batch_type = df_combined.pivot_table(
            values='录取分数线',
            index=['录取批次'],
            columns=['年份', '考生类别'],
            aggfunc='mean',
            fill_value=0
        ).round(1)
        pivot_batch_type.to_excel(writer, sheet_name='批次类别汇总')

        # 按地区分类汇总
        pivot_region_class = df_combined.groupby(['地区分类', '年份', '考生类别'])['录取分数线'].agg([
            'count', 'mean', 'median', 'std', 'min', 'max'
        ]).round(1).reset_index()
        pivot_region_class.to_excel(writer, sheet_name='地区分类统计', index=False)

        # 年度趋势分析
        yearly_trend = df_combined.groupby(['年份', '考生类别', '录取批次'])['录取分数线'].agg([
            'count', 'mean', 'std'
        ]).round(1).reset_index()
        yearly_trend.to_excel(writer, sheet_name='年度趋势', index=False)

    print(f"三年透视分析表已导出: {pivot_file}")

    # 4. 创建CSV格式（便于其他工具导入）
    csv_file = os.path.join(output_dir, '高考分数线合并数据_2021-2024.csv')
    df_combined.to_csv(csv_file, index=False, encoding='utf-8-sig')
    print(f"CSV格式已导出: {csv_file}")

    return output_dir

def analyze_3years_data(df_combined, df_2021, df_2023, df_2024):
    """三年数据分析"""
    print("\n=== 三年数据分析 ===")

    print("1. 基本统计信息")
    print(f"总数据量: {len(df_combined)}")
    print(f"2021年数据量: {len(df_2021)}")
    print(f"2023年数据量: {len(df_2023)}")
    print(f"2024年数据量: {len(df_2024)}")

    print("\n2. 各年份省份数量")
    print("2021年省份:", df_2021['地区'].nunique())
    print("2023年省份:", df_2023['地区'].nunique())
    print("2024年省份:", df_2024['地区'].nunique())

    print("\n3. 年度平均分数线变化")
    yearly_avg = df_combined.groupby('年份')['录取分数线'].mean().round(1)
    for year, avg_score in yearly_avg.items():
        print(f"{year}年平均分数线: {avg_score}分")

    print("\n4. 考生类别分布")
    category_dist = df_combined['考生类别'].value_counts()
    for category, count in category_dist.items():
        print(f"{category}: {count}条记录")

    print("\n5. 录取批次分布")
    batch_dist = df_combined['录取批次'].value_counts()
    for batch, count in batch_dist.items():
        print(f"{batch}: {count}条记录")

    return df_combined

def main():
    """主函数"""
    print("三年高考分数线数据规范化处理 (2021-2024)")
    print("=" * 60)

    try:
        # 加载和清理数据
        df_2021 = parse_2021_data()
        df_2023 = clean_2023_data()
        df_2024 = parse_and_clean_2024_data()

        if df_2021.empty or df_2023.empty or df_2024.empty:
            print("数据加载失败，请检查数据文件")
            return

        # 创建标准化数据集
        df_combined, df_2021_clean, df_2023_clean, df_2024_clean = create_standardized_datasets_3years(
            df_2021, df_2023, df_2024)

        # 数据分析
        analyze_3years_data(df_combined, df_2021_clean, df_2023_clean, df_2024_clean)

        # 导出FineBI数据文件
        output_dir = export_for_finebi_3years(df_combined, df_2021_clean, df_2023_clean, df_2024_clean)

        print("\n=== 三年数据规范化完成 ===")
        print(f"输出目录: {output_dir}")
        print("生成的文件:")
        print("- 高考分数线合并数据_2021-2024.xlsx (主要分析文件)")
        print("- 高考分数线_2021年.xlsx")
        print("- 高考分数线_2023年.xlsx")
        print("- 高考分数线_2024年.xlsx")
        print("- 高考分数线透视分析表_三年.xlsx")
        print("- 高考分数线合并数据_2021-2024.csv")

        # 数据质量报告
        print("\n=== 三年数据质量报告 ===")
        print(f"2021年数据: {len(df_2021_clean)}条记录，{df_2021_clean['地区'].nunique()}个地区")
        print(f"2023年数据: {len(df_2023_clean)}条记录，{df_2023_clean['地区'].nunique()}个地区")
        print(f"2024年数据: {len(df_2024_clean)}条记录，{df_2024_clean['地区'].nunique()}个地区")
        print(f"合并数据: {len(df_combined)}条记录")
        print(f"数据完整性: 无缺失值")
        print(f"数据时间范围: 2021, 2023-2024年")

    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
