#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的2021年数据解析器
"""

import pandas as pd
import re
from datetime import datetime

def parse_2021_data_improved():
    """改进的2021年数据解析"""
    print("=== 改进的2021年数据解析 ===")
    
    file_2021 = "[八爪鱼爬取]最全！31省区市2021年高考分数线完整版.xlsx"
    df_raw = pd.read_excel(file_2021)
    
    print(f"原始数据形状: {df_raw.shape}")
    
    data_2021 = []
    
    # 31个省市自治区完整列表
    all_provinces = [
        '北京', '天津', '河北', '山西', '内蒙古',
        '辽宁', '吉林', '黑龙江',
        '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东',
        '河南', '湖北', '湖南', '广东', '广西', '海南',
        '重庆', '四川', '贵州', '云南', '西藏',
        '陕西', '甘肃', '青海', '宁夏', '新疆'
    ]
    
    # 省份标题行映射
    province_title_lines = {}
    
    for index, row in df_raw.iterrows():
        text = str(row.iloc[0]).strip()
        
        if text == 'nan' or text == '':
            continue
            
        # 检查是否是省份标题行（省份名：分数信息）
        for province in all_provinces:
            if text.startswith(province + '：') or text.startswith(province + ':'):
                province_title_lines[province] = (index, text)
                break
    
    print(f"识别到的省份标题行: {len(province_title_lines)}个")
    
    # 解析每个省份的数据
    for province, (line_index, title_text) in province_title_lines.items():
        print(f"\n处理省份: {province}")
        print(f"标题行: {title_text}")
        
        # 解析标题行的分数信息
        scores = re.findall(r'(\d+)分', title_text)
        print(f"标题行分数: {scores}")
        
        if scores:
            # 解析标题行的批次和科目信息
            parse_province_title_line(province, title_text, scores, data_2021)
        
        # 查找该省份的详细信息行（通常在标题行后面几行）
        detail_lines = []
        for i in range(line_index + 1, min(line_index + 10, len(df_raw))):
            detail_text = str(df_raw.iloc[i, 0]).strip()
            if detail_text != 'nan' and detail_text != '':
                # 如果包含该省份名称或者包含分数信息，认为是详细信息
                if province in detail_text or re.search(r'\d+分', detail_text):
                    detail_lines.append((i, detail_text))
                else:
                    # 如果遇到其他省份名称，停止
                    if any(other_province in detail_text for other_province in all_provinces if other_province != province):
                        break
        
        print(f"详细信息行: {len(detail_lines)}行")
        for line_num, detail_text in detail_lines:
            print(f"  行{line_num}: {detail_text}")
            parse_province_detail_line(province, detail_text, data_2021)
    
    df_2021 = pd.DataFrame(data_2021)
    print(f"\n解析后数据形状: {df_2021.shape}")
    print(f"解析后省份数量: {df_2021['地区'].nunique()}")
    print(f"解析后省份列表: {sorted(df_2021['地区'].unique())}")
    
    return df_2021

def parse_province_title_line(province, text, scores, data_list):
    """解析省份标题行"""
    
    # 识别批次
    if '一本' in text:
        batch = '本科一批'
    elif '本科' in text and '特殊' not in text:
        batch = '本科批'
    elif '特殊' in text:
        batch = '特殊类型招生控制线'
    else:
        batch = '本科批'  # 默认
    
    # 识别科目类别并分配分数
    if ('文科' in text or '文史' in text) and ('理科' in text or '理工' in text):
        # 文理科都有
        if len(scores) >= 2:
            # 通常文科在前，理科在后，但需要根据文本顺序判断
            text_parts = text.split()
            wenke_score = None
            like_score = None
            
            # 查找文科和理科分数的位置
            for i, part in enumerate(text_parts):
                if ('文科' in part or '文史' in part) and re.search(r'(\d+)分', part):
                    score_match = re.search(r'(\d+)分', part)
                    if score_match:
                        wenke_score = int(score_match.group(1))
                elif ('理科' in part or '理工' in part) and re.search(r'(\d+)分', part):
                    score_match = re.search(r'(\d+)分', part)
                    if score_match:
                        like_score = int(score_match.group(1))
            
            # 如果没有在单独的部分找到，按顺序分配
            if wenke_score is None and like_score is None:
                if '文科' in text.split('理科')[0] or '文史' in text.split('理工')[0]:
                    wenke_score = int(scores[0])
                    like_score = int(scores[1])
                else:
                    like_score = int(scores[0])
                    wenke_score = int(scores[1])
            
            if wenke_score:
                data_list.append({
                    '地区': province,
                    '年份': 2021,
                    '考生类别': '文科',
                    '录取批次': batch,
                    '录取分数线': wenke_score,
                    '数据来源': '八爪鱼爬取',
                    '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
            
            if like_score:
                data_list.append({
                    '地区': province,
                    '年份': 2021,
                    '考生类别': '理科',
                    '录取批次': batch,
                    '录取分数线': like_score,
                    '数据来源': '八爪鱼爬取',
                    '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
    
    elif '历史' in text and '物理' in text:
        # 新高考：历史类和物理类
        if len(scores) >= 2:
            # 通常历史类在前，物理类在后
            if '历史' in text.split('物理')[0]:
                history_score = int(scores[0])
                physics_score = int(scores[1])
            else:
                physics_score = int(scores[0])
                history_score = int(scores[1])
            
            data_list.append({
                '地区': province,
                '年份': 2021,
                '考生类别': '历史类',
                '录取批次': batch,
                '录取分数线': history_score,
                '数据来源': '八爪鱼爬取',
                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
            
            data_list.append({
                '地区': province,
                '年份': 2021,
                '考生类别': '物理类',
                '录取批次': batch,
                '录取分数线': physics_score,
                '数据来源': '八爪鱼爬取',
                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
    
    elif len(scores) == 1:
        # 只有一个分数，可能是综合类或者单一科目
        if '综合' in text or '不分文理' in text:
            category = '综合'
        elif '文科' in text or '文史' in text:
            category = '文科'
        elif '理科' in text or '理工' in text:
            category = '理科'
        elif '历史' in text:
            category = '历史类'
        elif '物理' in text:
            category = '物理类'
        else:
            category = '综合'  # 默认
        
        data_list.append({
            '地区': province,
            '年份': 2021,
            '考生类别': category,
            '录取批次': batch,
            '录取分数线': int(scores[0]),
            '数据来源': '八爪鱼爬取',
            '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

def parse_province_detail_line(province, text, data_list):
    """解析省份详细信息行"""
    
    # 提取分数
    scores = re.findall(r'(\d+)分', text)
    
    if not scores:
        return
    
    # 识别批次
    batch = None
    if '本科一批' in text or '一本' in text or '重点本科' in text:
        batch = '本科一批'
    elif '本科二批' in text or '二本' in text or '普通本科' in text:
        batch = '本科二批'
    elif '专科' in text or '高职' in text:
        batch = '专科批'
    elif '特殊类型' in text or '特控' in text:
        batch = '特殊类型招生控制线'
    elif '本科' in text:
        batch = '本科批'
    
    if not batch:
        return  # 如果无法识别批次，跳过
    
    # 解析科目和分数
    if ('文科' in text or '文史' in text) and ('理科' in text or '理工' in text):
        # 文理科都有
        if len(scores) >= 2:
            # 根据文本中的顺序确定分数对应关系
            if text.find('文') < text.find('理'):
                wenke_score = int(scores[0])
                like_score = int(scores[1])
            else:
                like_score = int(scores[0])
                wenke_score = int(scores[1])
            
            data_list.append({
                '地区': province,
                '年份': 2021,
                '考生类别': '文科',
                '录取批次': batch,
                '录取分数线': wenke_score,
                '数据来源': '八爪鱼爬取',
                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
            
            data_list.append({
                '地区': province,
                '年份': 2021,
                '考生类别': '理科',
                '录取批次': batch,
                '录取分数线': like_score,
                '数据来源': '八爪鱼爬取',
                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
    
    elif '历史' in text and '物理' in text:
        # 新高考：历史类和物理类
        if len(scores) >= 2:
            if text.find('历史') < text.find('物理'):
                history_score = int(scores[0])
                physics_score = int(scores[1])
            else:
                physics_score = int(scores[0])
                history_score = int(scores[1])
            
            data_list.append({
                '地区': province,
                '年份': 2021,
                '考生类别': '历史类',
                '录取批次': batch,
                '录取分数线': history_score,
                '数据来源': '八爪鱼爬取',
                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
            
            data_list.append({
                '地区': province,
                '年份': 2021,
                '考生类别': '物理类',
                '录取批次': batch,
                '录取分数线': physics_score,
                '数据来源': '八爪鱼爬取',
                '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
    
    elif '文科' in text or '文史' in text:
        # 只有文科
        data_list.append({
            '地区': province,
            '年份': 2021,
            '考生类别': '文科',
            '录取批次': batch,
            '录取分数线': int(scores[0]),
            '数据来源': '八爪鱼爬取',
            '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
    
    elif '理科' in text or '理工' in text:
        # 只有理科
        data_list.append({
            '地区': province,
            '年份': 2021,
            '考生类别': '理科',
            '录取批次': batch,
            '录取分数线': int(scores[0]),
            '数据来源': '八爪鱼爬取',
            '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
    
    elif '历史' in text:
        # 只有历史类
        data_list.append({
            '地区': province,
            '年份': 2021,
            '考生类别': '历史类',
            '录取批次': batch,
            '录取分数线': int(scores[0]),
            '数据来源': '八爪鱼爬取',
            '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
    
    elif '物理' in text:
        # 只有物理类
        data_list.append({
            '地区': province,
            '年份': 2021,
            '考生类别': '物理类',
            '录取批次': batch,
            '录取分数线': int(scores[0]),
            '数据来源': '八爪鱼爬取',
            '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

def main():
    """主函数"""
    print("改进的2021年数据解析器")
    print("=" * 50)
    
    df_2021 = parse_2021_data_improved()
    
    # 显示解析结果统计
    print(f"\n=== 解析结果统计 ===")
    print(f"总记录数: {len(df_2021)}")
    print(f"省份数量: {df_2021['地区'].nunique()}")
    
    print(f"\n各省份记录数:")
    province_counts = df_2021['地区'].value_counts().sort_index()
    for province, count in province_counts.items():
        print(f"{province}: {count}条")
    
    print(f"\n考生类别分布:")
    category_counts = df_2021['考生类别'].value_counts()
    for category, count in category_counts.items():
        print(f"{category}: {count}条")
    
    print(f"\n录取批次分布:")
    batch_counts = df_2021['录取批次'].value_counts()
    for batch, count in batch_counts.items():
        print(f"{batch}: {count}条")

if __name__ == "__main__":
    main()
