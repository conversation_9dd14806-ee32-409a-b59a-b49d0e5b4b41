#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高考成绩数据分析项目
作者：数据分析小组
日期：2024年
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib import rcParams
import warnings
import re
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_and_clean_2023_data():
    """加载并清理2023年数据"""
    print("=== 加载2023年数据 ===")

    file_2023 = "[八爪鱼爬取]2023年全国各地高考分数线公布汇总：一本、二本、专科.xlsx"
    df_2023 = pd.read_excel(file_2023)

    print(f"原始数据形状: {df_2023.shape}")

    # 删除重复的表头行
    df_2023 = df_2023[df_2023['地区'] != '地区'].copy()

    # 转换录取分数线为数值类型
    df_2023['录取分数线'] = pd.to_numeric(df_2023['录取分数线'], errors='coerce')

    # 删除包含NaN的行
    df_2023 = df_2023.dropna()

    print(f"清理后数据形状: {df_2023.shape}")
    print("清理后数据前5行:")
    print(df_2023.head())

    return df_2023

def parse_2024_data():
    """解析2024年数据"""
    print("\n=== 解析2024年数据 ===")

    file_2024 = "[八爪鱼爬取]全国2024年高考录取分数线一览表汇总（含各省本科线、专科线）-高考100.xlsx"
    df_raw = pd.read_excel(file_2024)

    # 解析数据
    data_2024 = []
    current_province = None

    for index, row in df_raw.iterrows():
        text = str(row.iloc[0]).strip()

        # 检查是否是省份名称（以数字开头）
        if re.match(r'^\d+\.', text):
            current_province = re.sub(r'^\d+\.', '', text).strip()
            continue

        if current_province and '：' in text:
            # 解析分数线信息
            if '本科一批' in text or '本科二批' in text or '专科批' in text:
                batch = None
                if '本科一批' in text:
                    batch = '本科一批'
                elif '本科二批' in text:
                    batch = '本科二批'
                elif '专科批' in text:
                    batch = '专科批'

                # 提取分数
                scores = re.findall(r'(\d+)分', text)

                # 判断文理科
                if '理科' in text and '文科' in text:
                    if len(scores) >= 2:
                        data_2024.append({
                            '地区': current_province,
                            '年份': '2024',
                            '考生类别': '理科',
                            '录取批次': batch,
                            '录取分数线': int(scores[0])
                        })
                        data_2024.append({
                            '地区': current_province,
                            '年份': '2024',
                            '考生类别': '文科',
                            '录取批次': batch,
                            '录取分数线': int(scores[1])
                        })
                elif '理工' in text and '文史' in text:
                    if len(scores) >= 2:
                        data_2024.append({
                            '地区': current_province,
                            '年份': '2024',
                            '考生类别': '理科',
                            '录取批次': batch,
                            '录取分数线': int(scores[0])
                        })
                        data_2024.append({
                            '地区': current_province,
                            '年份': '2024',
                            '考生类别': '文科',
                            '录取批次': batch,
                            '录取分数线': int(scores[1])
                        })
                elif '综合' in text:
                    if len(scores) >= 1:
                        data_2024.append({
                            '地区': current_province,
                            '年份': '2024',
                            '考生类别': '综合',
                            '录取批次': batch,
                            '录取分数线': int(scores[0])
                        })

    df_2024 = pd.DataFrame(data_2024)
    print(f"解析后2024年数据形状: {df_2024.shape}")
    print("解析后数据前10行:")
    print(df_2024.head(10))

    return df_2024

def analyze_data(df_2023, df_2024):
    """数据分析"""
    print("\n=== 数据分析 ===")

    # 合并数据
    df_combined = pd.concat([df_2023, df_2024], ignore_index=True)

    print("1. 基本统计信息")
    print(f"总数据量: {len(df_combined)}")
    print(f"2023年数据量: {len(df_2023)}")
    print(f"2024年数据量: {len(df_2024)}")

    print("\n2. 各年份省份数量")
    print("2023年省份:", df_2023['地区'].nunique())
    print("2024年省份:", df_2024['地区'].nunique())

    print("\n3. 录取批次分布")
    print("2023年批次分布:")
    print(df_2023['录取批次'].value_counts())
    print("\n2024年批次分布:")
    print(df_2024['录取批次'].value_counts())

    print("\n4. 考生类别分布")
    print("2023年考生类别:")
    print(df_2023['考生类别'].value_counts())
    print("\n2024年考生类别:")
    print(df_2024['考生类别'].value_counts())

    return df_combined

def create_visualizations(df_2023, df_2024, df_combined):
    """创建可视化图表"""
    print("\n=== 创建可视化图表 ===")

    # 创建图表目录
    if not os.path.exists('charts'):
        os.makedirs('charts')

    # 1. 2023-2024年本科一批分数线对比
    plt.figure(figsize=(15, 8))

    # 筛选本科一批数据
    df_2023_ben1 = df_2023[df_2023['录取批次'].str.contains('本科一批|本科批', na=False)]
    df_2024_ben1 = df_2024[df_2024['录取批次'].str.contains('本科一批|本科批', na=False)]

    if not df_2023_ben1.empty and not df_2024_ben1.empty:
        # 按省份和科目分组计算平均分
        avg_2023 = df_2023_ben1.groupby(['地区', '考生类别'])['录取分数线'].mean().reset_index()
        avg_2024 = df_2024_ben1.groupby(['地区', '考生类别'])['录取分数线'].mean().reset_index()

        # 合并数据
        avg_2023['年份'] = '2023'
        avg_2024['年份'] = '2024'
        avg_combined = pd.concat([avg_2023, avg_2024])

        # 创建对比图
        plt.subplot(2, 2, 1)
        sns.boxplot(data=avg_combined, x='年份', y='录取分数线', hue='考生类别')
        plt.title('2023-2024年本科批分数线对比')
        plt.ylabel('分数线')

    # 2. 各省份分数线分布
    plt.subplot(2, 2, 2)
    if not df_combined.empty:
        # 选择主要省份
        top_provinces = df_combined['地区'].value_counts().head(10).index
        df_top = df_combined[df_combined['地区'].isin(top_provinces)]

        sns.boxplot(data=df_top, x='地区', y='录取分数线')
        plt.xticks(rotation=45)
        plt.title('主要省份分数线分布')
        plt.ylabel('分数线')

    # 3. 文理科分数线差异
    plt.subplot(2, 2, 3)
    df_wenli = df_combined[df_combined['考生类别'].isin(['文科', '理科'])]
    if not df_wenli.empty:
        sns.violinplot(data=df_wenli, x='考生类别', y='录取分数线')
        plt.title('文理科分数线分布')
        plt.ylabel('分数线')

    # 4. 年份趋势
    plt.subplot(2, 2, 4)
    yearly_avg = df_combined.groupby(['年份', '考生类别'])['录取分数线'].mean().reset_index()
    sns.lineplot(data=yearly_avg, x='年份', y='录取分数线', hue='考生类别', marker='o')
    plt.title('年份分数线趋势')
    plt.ylabel('平均分数线')

    plt.tight_layout()
    plt.savefig('charts/高考分数线综合分析.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("图表已保存到 charts/ 目录")

def generate_report(df_2023, df_2024, df_combined):
    """生成分析报告"""
    print("\n=== 生成分析报告 ===")

    report = []
    report.append("# 2023-2024年全国高考分数线数据分析报告\n")
    report.append("## 1. 数据概况\n")
    report.append(f"- 2023年数据量：{len(df_2023)}条记录")
    report.append(f"- 2024年数据量：{len(df_2024)}条记录")
    report.append(f"- 总数据量：{len(df_combined)}条记录\n")

    report.append("## 2. 主要发现\n")

    # 计算平均分数线
    if not df_combined.empty:
        avg_2023 = df_2023['录取分数线'].mean()
        avg_2024 = df_2024['录取分数线'].mean()
        report.append(f"- 2023年平均分数线：{avg_2023:.1f}分")
        report.append(f"- 2024年平均分数线：{avg_2024:.1f}分")
        report.append(f"- 年度变化：{avg_2024-avg_2023:+.1f}分\n")

    # 省份分析
    provinces_2023 = set(df_2023['地区'].unique())
    provinces_2024 = set(df_2024['地区'].unique())
    common_provinces = provinces_2023.intersection(provinces_2024)

    report.append(f"## 3. 省份覆盖情况\n")
    report.append(f"- 2023年覆盖省份：{len(provinces_2023)}个")
    report.append(f"- 2024年覆盖省份：{len(provinces_2024)}个")
    report.append(f"- 共同覆盖省份：{len(common_provinces)}个\n")

    # 保存报告
    with open('高考分数线分析报告.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))

    print("分析报告已保存为：高考分数线分析报告.md")

def main():
    """主函数"""
    print("高考成绩数据分析项目启动")
    print("=" * 50)

    try:
        # 加载和清理数据
        df_2023 = load_and_clean_2023_data()
        df_2024 = parse_2024_data()

        if df_2023.empty or df_2024.empty:
            print("数据加载失败，请检查数据文件")
            return

        # 数据分析
        df_combined = analyze_data(df_2023, df_2024)

        # 创建可视化
        create_visualizations(df_2023, df_2024, df_combined)

        # 生成报告
        generate_report(df_2023, df_2024, df_combined)

        print("\n=== 分析完成 ===")
        print("生成的文件：")
        print("- charts/高考分数线综合分析.png")
        print("- 高考分数线分析报告.md")

    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
