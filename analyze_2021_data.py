#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析2021年高考分数线数据结构
"""

import pandas as pd
import re

def analyze_2021_data():
    """分析2021年数据结构"""
    print("=== 分析2021年数据结构 ===")
    
    file_2021 = "[八爪鱼爬取]最全！31省区市2021年高考分数线完整版.xlsx"
    
    try:
        # 尝试读取所有工作表
        excel_file = pd.ExcelFile(file_2021)
        print(f"工作表列表: {excel_file.sheet_names}")
        
        # 读取第一个工作表
        df_2021 = pd.read_excel(file_2021, sheet_name=0)
        print(f"\n第一个工作表数据形状: {df_2021.shape}")
        print(f"列名: {df_2021.columns.tolist()}")
        
        print("\n前20行数据:")
        for i in range(min(20, len(df_2021))):
            row_data = []
            for col in df_2021.columns:
                value = str(df_2021.iloc[i][col]).strip()
                row_data.append(value)
            print(f"{i:2d}: {' | '.join(row_data)}")
        
        # 如果有多个工作表，也查看其他的
        if len(excel_file.sheet_names) > 1:
            for sheet_name in excel_file.sheet_names[1:]:
                print(f"\n=== 工作表: {sheet_name} ===")
                df_sheet = pd.read_excel(file_2021, sheet_name=sheet_name)
                print(f"数据形状: {df_sheet.shape}")
                print(f"列名: {df_sheet.columns.tolist()}")
                print("前5行:")
                print(df_sheet.head())
        
        return df_2021, excel_file.sheet_names
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None, []

def main():
    """主函数"""
    print("分析2021年高考分数线数据")
    print("=" * 50)
    
    df_2021, sheet_names = analyze_2021_data()
    
    if df_2021 is not None:
        print(f"\n=== 数据概览 ===")
        print(f"总行数: {len(df_2021)}")
        print(f"总列数: {len(df_2021.columns)}")
        print(f"工作表数量: {len(sheet_names)}")

if __name__ == "__main__":
    main()
