import pandas as pd
import os

print("当前工作目录:", os.getcwd())
print("文件列表:", os.listdir('.'))

try:
    # 尝试读取2023年数据
    file1 = "[八爪鱼爬取]2023年全国各地高考分数线公布汇总：一本、二本、专科.xlsx"
    if os.path.exists(file1):
        df1 = pd.read_excel(file1)
        print(f"2023年数据加载成功，形状: {df1.shape}")
        print("列名:", df1.columns.tolist())
        print("前3行数据:")
        print(df1.head(3))
    else:
        print(f"文件不存在: {file1}")
        
except Exception as e:
    print(f"读取2023年数据出错: {e}")

try:
    # 尝试读取2024年数据
    file2 = "[八爪鱼爬取]全国2024年高考录取分数线一览表汇总（含各省本科线、专科线）-高考100.xlsx"
    if os.path.exists(file2):
        df2 = pd.read_excel(file2)
        print(f"\n2024年数据加载成功，形状: {df2.shape}")
        print("列名:", df2.columns.tolist())
        print("前3行数据:")
        print(df2.head(3))
    else:
        print(f"文件不存在: {file2}")
        
except Exception as e:
    print(f"读取2024年数据出错: {e}")
