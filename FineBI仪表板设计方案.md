# FineBI高考分数线数据仪表板设计方案（三年版）

## 🎯 仪表板整体架构

### 四层仪表板设计
1. **概览仪表板** - 高层管理视图
2. **三年趋势仪表板** - 时间序列分析视图
3. **对比分析仪表板** - 深度分析视图
4. **详细数据仪表板** - 操作层视图

## 📊 数据概况（2021-2024年）
- **总数据量**: 387条记录
- **时间跨度**: 2021年、2023年、2024年
- **地区覆盖**: 31个省市自治区
- **考生类别**: 文科、理科、物理类、历史类、综合
- **年度平均分数线变化**:
  - 2021年: 466.0分
  - 2023年: 355.6分
  - 2024年: 371.5分

---

## 📊 仪表板一：概览仪表板

### 布局设计（4x3网格）
```
┌─────────────┬─────────────┬─────────────┐
│  核心指标卡  │  核心指标卡  │  核心指标卡  │
├─────────────┼─────────────┼─────────────┤
│     全国分数线趋势图（占2格）    │   地区分布  │
├─────────────┼─────────────┼─────────────┤
│   批次分布饼图  │  科目对比图  │  等级分布图  │
└─────────────┴─────────────┴─────────────┘
```

### 组件配置详情

#### 1. 核心指标卡组件（3个）
**组件类型**: 指标卡
- **指标卡1**: 
  - 标题: "2024年全国平均分数线"
  - 指标: 录取分数线（平均值）
  - 筛选条件: 年份=2024
  - 样式: 大号字体，蓝色主题

- **指标卡2**: 
  - 标题: "年度变化"
  - 指标: 2024年平均分-2023年平均分
  - 样式: 根据正负值显示红绿色

- **指标卡3**: 
  - 标题: "覆盖省份数"
  - 指标: 地区（去重计数）
  - 样式: 橙色主题

#### 2. 全国分数线趋势图
**组件类型**: 折线图
- **X轴**: 年份
- **Y轴**: 录取分数线（平均值）
- **分组**: 考生类别
- **筛选**: 录取批次 in [本科一批, 本科批]
- **样式**: 
  - 线条粗细: 3px
  - 数据点显示
  - 图例位置: 右上角

#### 3. 地区分布图
**组件类型**: 中国地图
- **地理字段**: 地区
- **指标**: 录取分数线（平均值）
- **筛选**: 年份=2024, 录取批次=本科一批
- **颜色**: 渐变色（浅蓝到深蓝）
- **提示框**: 显示省份名称和具体分数

#### 4. 批次分布饼图
**组件类型**: 饼图
- **维度**: 录取批次
- **指标**: 录取分数线（记录数）
- **样式**: 
  - 显示百分比
  - 图例位置: 右侧
  - 颜色: 彩虹色系

#### 5. 科目对比图
**组件类型**: 柱状图
- **X轴**: 考生类别
- **Y轴**: 录取分数线（平均值）
- **分组**: 年份
- **筛选**: 录取批次=本科一批
- **样式**: 并排柱状图

#### 6. 等级分布图
**组件类型**: 环形图
- **维度**: 分数线等级
- **指标**: 录取分数线（记录数）
- **筛选**: 年份=2024
- **样式**: 内圈显示总数

---

## 📈 仪表板二：三年趋势仪表板

### 布局设计（3x2网格）
```
┌─────────────┬─────────────┬─────────────┐
│    年度对比指标卡（占3格）           │
├─────────────┼─────────────┼─────────────┤
│  三年趋势折线图（占2格）    │  年度变化率  │
└─────────────┴─────────────┴─────────────┘
```

### 组件配置详情

#### 1. 年度对比指标卡（3个）
**组件类型**: 指标卡
- **指标卡1**:
  - 标题: "2021年平均分数线"
  - 指标: 录取分数线（平均值）
  - 筛选条件: 年份=2021
  - 样式: 大号字体，绿色主题

- **指标卡2**:
  - 标题: "2023年平均分数线"
  - 指标: 录取分数线（平均值）
  - 筛选条件: 年份=2023
  - 样式: 大号字体，蓝色主题

- **指标卡3**:
  - 标题: "2024年平均分数线"
  - 指标: 录取分数线（平均值）
  - 筛选条件: 年份=2024
  - 样式: 大号字体，橙色主题

#### 2. 三年趋势折线图
**组件类型**: 折线图
- **X轴**: 年份
- **Y轴**: 录取分数线（平均值）
- **分组**: 考生类别
- **筛选**: 录取批次 in [本科一批, 本科批]
- **样式**:
  - 线条粗细: 4px
  - 数据点显示
  - 趋势线显示
  - 图例位置: 右上角

#### 3. 年度变化率图
**组件类型**: 柱状图
- **X轴**: 年份区间（2021-2023, 2023-2024）
- **Y轴**: 分数线变化率（%）
- **计算字段**: (当年平均分-上年平均分)/上年平均分*100
- **样式**:
  - 正值绿色，负值红色
  - 显示数据标签

---

## 📊 仪表板三：对比分析仪表板

### 布局设计（3x3网格）
```
┌─────────────┬─────────────┬─────────────┐
│    筛选器区域（占3格）           │
├─────────────┼─────────────┼─────────────┤
│  省份对比柱状图（占2格）    │   热力图    │
├─────────────┼─────────────┼─────────────┤
│  散点图     │  雷达图     │  箱线图     │
└─────────────┴─────────────┴─────────────┘
```

### 组件配置详情

#### 1. 筛选器区域
**组件类型**: 筛选组件
- **年份筛选器**: 复选框，默认全选
- **地区筛选器**: 下拉多选，支持搜索
- **批次筛选器**: 单选，默认"本科一批"
- **科目筛选器**: 复选框，默认全选

#### 2. 省份对比柱状图
**组件类型**: 柱状图
- **X轴**: 地区（按分数线降序排列）
- **Y轴**: 录取分数线
- **分组**: 年份
- **样式**: 
  - 并排柱状图
  - 显示数据标签
  - X轴标签旋转45度

#### 3. 分数线热力图
**组件类型**: 热力图
- **行**: 地区分类
- **列**: 录取批次
- **值**: 录取分数线（平均值）
- **颜色**: 红黄绿渐变
- **样式**: 显示数值

#### 4. 文理科关系散点图
**组件类型**: 散点图
- **X轴**: 理科分数线
- **Y轴**: 文科分数线
- **分组**: 地区分类
- **大小**: 固定
- **样式**: 添加趋势线

#### 5. 地区综合雷达图
**组件类型**: 雷达图
- **维度**: 录取批次
- **指标**: 录取分数线（平均值）
- **分组**: 选定地区（通过筛选器控制）
- **样式**: 填充半透明

#### 6. 分数线分布箱线图
**组件类型**: 箱线图
- **X轴**: 地区分类
- **Y轴**: 录取分数线
- **分组**: 年份
- **样式**: 显示异常值

---

## 📋 仪表板三：详细数据仪表板

### 布局设计（2x2网格）
```
┌─────────────┬─────────────┐
│    高级筛选器（占2格）      │
├─────────────┼─────────────┤
│    详细数据表（占2格）      │
└─────────────┴─────────────┘
```

### 组件配置详情

#### 1. 高级筛选器
**组件类型**: 筛选组件
- **年份**: 滑块筛选器
- **地区**: 树形筛选器（按地区分类分组）
- **分数线范围**: 区间筛选器
- **批次**: 列表筛选器
- **科目**: 标签筛选器

#### 2. 详细数据表
**组件类型**: 明细表
- **列字段**: 
  - 地区
  - 年份  
  - 考生类别
  - 录取批次
  - 录取分数线
  - 分数线等级
  - 地区分类
- **功能**: 
  - 支持排序
  - 支持导出
  - 分页显示
  - 条件格式（分数线高低用颜色区分）

---

## 🎨 样式和交互设计

### 整体主题
- **主色调**: 蓝色系（#1890FF）
- **辅助色**: 橙色（#FA8C16）、绿色（#52C41A）
- **背景色**: 浅灰色（#F5F5F5）
- **字体**: 微软雅黑

### 交互功能
1. **联动筛选**: 所有筛选器与图表联动
2. **钻取功能**: 地图可钻取到省份详情
3. **提示框**: 鼠标悬停显示详细信息
4. **导出功能**: 支持图表和数据导出

### 响应式设计
- **PC端**: 3列布局
- **平板端**: 2列布局  
- **手机端**: 1列布局

---

## 📱 移动端适配建议

### 移动端仪表板（单独设计）
1. **核心指标卡**（上下排列）
2. **简化趋势图**
3. **省份排行榜**（列表形式）
4. **快速筛选器**

---

## 🔧 实施步骤

### 第一阶段：基础搭建
1. 导入数据源
2. 创建基础图表
3. 设置筛选器

### 第二阶段：美化优化
1. 统一样式主题
2. 添加交互功能
3. 优化布局

### 第三阶段：测试发布
1. 功能测试
2. 性能优化
3. 用户培训

---

## 💡 高级功能建议

### 1. 预警功能
- 设置分数线异常预警
- 年度变化幅度预警

### 2. 自动刷新
- 设置数据自动刷新时间
- 新数据到达提醒

### 3. 权限控制
- 不同用户查看不同数据范围
- 敏感数据脱敏显示

### 4. 导出报告
- 一键生成PDF报告
- 定时邮件发送报告

---

## 📋 FineBI操作清单

### 数据准备
1. **导入数据源**
   - 文件路径: `finebi_data_3years/高考分数线合并数据_2021-2024.xlsx`
   - 数据连接类型: Excel文件
   - 编码格式: UTF-8
   - 数据量: 387条记录，覆盖2021、2023、2024年

2. **字段设置**
   ```
   地区 -> 维度字段 (文本)
   年份 -> 维度字段 (数值，设为离散)
   考生类别 -> 维度字段 (文本)
   录取批次 -> 维度字段 (文本)
   录取分数线 -> 指标字段 (数值)
   分数线等级 -> 维度字段 (文本)
   地区分类 -> 维度字段 (文本)
   ```

### 快速创建组件模板

#### 模板1: 核心指标卡
```
组件类型: 指标卡
数据配置:
- 指标: SUM(录取分数线)/COUNT(录取分数线)
- 筛选: 年份=2024
样式配置:
- 字体大小: 36px
- 颜色: #1890FF
- 显示单位: 分
```

#### 模板2: 三年趋势折线图
```
组件类型: 折线图
数据配置:
- X轴: 年份 (2021, 2023, 2024)
- Y轴: AVG(录取分数线)
- 分组: 考生类别
- 筛选: 录取批次 IN ['本科一批','本科批']
样式配置:
- 线条宽度: 4px
- 数据点: 显示
- 趋势线: 显示
- 图例位置: 右上角
- Y轴范围: 自动调整
```

#### 模板3: 省份对比柱状图
```
组件类型: 柱状图
数据配置:
- X轴: 地区 (按AVG(录取分数线)降序)
- Y轴: AVG(录取分数线)
- 分组: 年份
样式配置:
- 柱状图类型: 并排
- 数据标签: 显示
- X轴标签角度: 45度
```

#### 模板4: 地区热力图
```
组件类型: 热力图
数据配置:
- 行: 地区分类
- 列: 录取批次
- 值: AVG(录取分数线)
样式配置:
- 颜色方案: 红黄绿渐变
- 数值显示: 开启
- 边框: 1px白色
```

### 筛选器配置模板

#### 年份筛选器
```
类型: 复选框筛选器
字段: 年份
默认值: 全选
样式: 水平排列
```

#### 地区筛选器
```
类型: 下拉筛选器
字段: 地区
默认值: 全选
功能: 支持搜索
最大显示: 10个
```

#### 批次筛选器
```
类型: 单选筛选器
字段: 录取批次
默认值: 本科一批
样式: 按钮组
```

### 仪表板布局代码
```css
/* 概览仪表板布局 */
.dashboard-overview {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 120px 300px 250px;
  gap: 15px;
  padding: 20px;
}

.metric-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 20px;
  text-align: center;
}

.chart-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 15px;
}
```
