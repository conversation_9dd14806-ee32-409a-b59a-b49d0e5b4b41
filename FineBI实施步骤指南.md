# FineBI高考分数线仪表板实施步骤指南

## 🚀 第一步：数据源配置

### 1.1 导入Excel数据
1. 打开FineBI，点击"数据准备" → "添加数据连接"
2. 选择"文件数据集" → "Excel"
3. 上传文件：`finebi_data/高考分数线合并数据_2023-2024.xlsx`
4. 数据集名称：`高考分数线数据`

### 1.2 字段类型设置
```
字段名称        字段类型    设置说明
地区           维度        文本类型
年份           维度        数值类型，设为离散
考生类别       维度        文本类型
录取批次       维度        文本类型
录取分数线     指标        数值类型，聚合方式：平均值
分数线等级     维度        文本类型
地区分类       维度        文本类型
数据来源       维度        文本类型（可隐藏）
更新时间       维度        日期类型（可隐藏）
```

---

## 📊 第二步：创建概览仪表板

### 2.1 新建仪表板
1. 点击"仪表板" → "新建仪表板"
2. 仪表板名称：`高考分数线概览`
3. 选择布局：自定义布局

### 2.2 创建核心指标卡

#### 指标卡1：2024年平均分数线
1. 拖拽"指标卡"组件到画布
2. 数据配置：
   - 指标：录取分数线（平均值）
   - 筛选条件：年份 = 2024
3. 样式配置：
   - 标题：2024年全国平均分数线
   - 字体大小：36px
   - 颜色：蓝色主题
   - 单位：分

#### 指标卡2：年度变化
1. 添加"指标卡"组件
2. 数据配置：
   - 使用计算字段：AVG(IF(年份=2024,录取分数线,0)) - AVG(IF(年份=2023,录取分数线,0))
3. 样式配置：
   - 标题：年度变化
   - 条件格式：正数绿色，负数红色
   - 显示+/-符号

#### 指标卡3：覆盖省份数
1. 添加"指标卡"组件
2. 数据配置：
   - 指标：地区（去重计数）
3. 样式配置：
   - 标题：覆盖省份数
   - 颜色：橙色主题
   - 单位：个

### 2.3 创建趋势折线图
1. 拖拽"折线图"组件
2. 数据配置：
   - X轴：年份
   - Y轴：录取分数线（平均值）
   - 分组：考生类别
   - 筛选：录取批次 包含 "本科一批" 或 "本科批"
3. 样式配置：
   - 标题：全国分数线趋势
   - 线条宽度：3px
   - 显示数据点
   - 图例位置：右上角

### 2.4 创建地区分布地图
1. 拖拽"地图"组件
2. 数据配置：
   - 地理字段：地区
   - 指标：录取分数线（平均值）
   - 筛选：年份=2024，录取批次=本科一批
3. 样式配置：
   - 地图类型：中国地图
   - 颜色：蓝色渐变
   - 显示数值标签

### 2.5 创建其他图表
按照设计方案依次创建：
- 批次分布饼图
- 科目对比柱状图  
- 等级分布环形图

---

## 📈 第三步：创建对比分析仪表板

### 3.1 新建仪表板
1. 新建仪表板：`高考分数线对比分析`
2. 选择3x3网格布局

### 3.2 添加筛选器区域
1. 拖拽"筛选组件"到顶部
2. 配置筛选器：
   - 年份筛选器：复选框类型
   - 地区筛选器：下拉多选类型
   - 批次筛选器：单选按钮类型
   - 科目筛选器：复选框类型

### 3.3 创建省份对比柱状图
1. 拖拽"柱状图"组件
2. 数据配置：
   - X轴：地区
   - Y轴：录取分数线（平均值）
   - 分组：年份
   - 排序：按Y轴降序
3. 样式配置：
   - 柱状图类型：并排
   - X轴标签角度：45度
   - 显示数据标签

### 3.4 创建热力图
1. 拖拽"热力图"组件
2. 数据配置：
   - 行：地区分类
   - 列：录取批次
   - 值：录取分数线（平均值）
3. 样式配置：
   - 颜色方案：红黄绿渐变
   - 显示数值
   - 边框：白色1px

### 3.5 创建其他分析图表
- 散点图：文理科关系分析
- 雷达图：地区综合对比
- 箱线图：分数线分布分析

---

## 📋 第四步：创建详细数据仪表板

### 4.1 新建仪表板
1. 新建仪表板：`高考分数线详细数据`
2. 选择2x2布局

### 4.2 配置高级筛选器
1. 添加多个筛选组件：
   - 年份滑块筛选器
   - 地区树形筛选器
   - 分数线区间筛选器
   - 批次列表筛选器

### 4.3 创建详细数据表
1. 拖拽"明细表"组件
2. 配置列字段：
   - 地区、年份、考生类别、录取批次、录取分数线、分数线等级、地区分类
3. 功能设置：
   - 启用排序
   - 启用分页（每页50条）
   - 启用导出功能
   - 条件格式：分数线高低用颜色区分

---

## 🎨 第五步：样式美化

### 5.1 统一主题
1. 设置仪表板主题：
   - 主色调：#1890FF（蓝色）
   - 辅助色：#FA8C16（橙色）
   - 背景色：#F5F5F5（浅灰）

### 5.2 组件样式
1. 标题样式：
   - 字体：微软雅黑
   - 大小：16px
   - 颜色：#262626

2. 图表样式：
   - 背景：白色
   - 圆角：8px
   - 阴影：0 2px 8px rgba(0,0,0,0.1)

### 5.3 布局优化
1. 组件间距：15px
2. 页面边距：20px
3. 响应式设置：支持不同屏幕尺寸

---

## 🔧 第六步：交互功能

### 6.1 联动设置
1. 设置筛选器与图表联动
2. 配置图表间的钻取关系
3. 添加鼠标悬停提示

### 6.2 权限控制
1. 设置用户访问权限
2. 配置数据行级权限
3. 设置功能权限（导出、编辑等）

---

## ✅ 第七步：测试发布

### 7.1 功能测试
- [ ] 数据显示正确性
- [ ] 筛选器功能
- [ ] 图表交互
- [ ] 导出功能
- [ ] 移动端适配

### 7.2 性能优化
- [ ] 查询性能测试
- [ ] 大数据量测试
- [ ] 并发访问测试

### 7.3 用户培训
1. 制作使用手册
2. 组织培训会议
3. 收集用户反馈

---

## 📱 第八步：移动端配置

### 8.1 创建移动端仪表板
1. 新建移动端专用仪表板
2. 简化组件布局
3. 优化触摸交互

### 8.2 响应式设计
1. 设置断点：768px, 1024px
2. 配置不同屏幕下的布局
3. 测试各种设备兼容性

---

## 🔄 第九步：维护更新

### 9.1 数据更新
1. 设置自动刷新计划
2. 配置数据更新提醒
3. 建立数据质量监控

### 9.2 功能迭代
1. 收集用户需求
2. 定期功能优化
3. 版本管理

---

## 💡 实施建议

### 优先级排序
1. **高优先级**：概览仪表板 + 基础筛选
2. **中优先级**：对比分析仪表板
3. **低优先级**：详细数据仪表板 + 高级功能

### 时间规划
- 第1-2天：数据准备和概览仪表板
- 第3-4天：对比分析仪表板
- 第5天：详细数据仪表板和测试
- 第6天：美化和优化
- 第7天：培训和发布

### 注意事项
1. 先完成核心功能，再添加高级特性
2. 重视用户体验和响应速度
3. 做好数据备份和版本控制
4. 建立完善的文档体系
