#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据规范化脚本 - 为FineBI准备标准化数据
作者：数据分析小组
日期：2024年
"""

import pandas as pd
import numpy as np
import re
import os
from datetime import datetime

def clean_2023_data():
    """清理和规范化2023年数据"""
    print("=== 处理2023年数据 ===")
    
    # 读取2023年数据
    file_2023 = "[八爪鱼爬取]2023年全国各地高考分数线公布汇总：一本、二本、专科.xlsx"
    df_2023 = pd.read_excel(file_2023)
    
    print(f"原始数据形状: {df_2023.shape}")
    print("原始列名:", df_2023.columns.tolist())
    
    # 删除重复的表头行
    df_2023 = df_2023[df_2023['地区'] != '地区'].copy()
    
    # 数据类型转换
    df_2023['年份'] = pd.to_numeric(df_2023['年份'], errors='coerce')
    df_2023['录取分数线'] = pd.to_numeric(df_2023['录取分数线'], errors='coerce')
    
    # 删除包含NaN的行
    df_2023 = df_2023.dropna()
    
    # 标准化地区名称（去除空格）
    df_2023['地区'] = df_2023['地区'].str.strip()
    
    # 标准化考生类别
    df_2023['考生类别'] = df_2023['考生类别'].str.strip()
    
    # 标准化录取批次
    df_2023['录取批次'] = df_2023['录取批次'].str.strip()
    
    # 添加数据来源标识
    df_2023['数据来源'] = '八爪鱼爬取'
    df_2023['更新时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    print(f"清理后数据形状: {df_2023.shape}")
    print("清理后前5行:")
    print(df_2023.head())
    
    return df_2023

def parse_and_clean_2024_data():
    """解析和规范化2024年数据"""
    print("\n=== 处理2024年数据 ===")
    
    # 读取2024年原始数据
    file_2024 = "[八爪鱼爬取]全国2024年高考录取分数线一览表汇总（含各省本科线、专科线）-高考100.xlsx"
    df_raw = pd.read_excel(file_2024)
    
    print(f"原始数据形状: {df_raw.shape}")
    
    # 解析数据
    data_2024 = []
    current_province = None
    
    for index, row in df_raw.iterrows():
        text = str(row.iloc[0]).strip()
        
        # 检查是否是省份名称（以数字开头）
        if re.match(r'^\d+\.', text):
            current_province = re.sub(r'^\d+\.', '', text).strip()
            continue
        
        if current_province and '：' in text:
            # 解析分数线信息
            if any(batch in text for batch in ['本科一批', '本科二批', '专科批', '本科批']):
                batch = None
                if '本科一批' in text:
                    batch = '本科一批'
                elif '本科二批' in text:
                    batch = '本科二批'
                elif '专科批' in text:
                    batch = '专科批'
                elif '本科批' in text:
                    batch = '本科批'
                
                # 提取分数
                scores = re.findall(r'(\d+)分', text)
                
                # 判断文理科
                if '理科' in text and '文科' in text and len(scores) >= 2:
                    data_2024.append({
                        '地区': current_province,
                        '年份': 2024,
                        '考生类别': '理科',
                        '录取批次': batch,
                        '录取分数线': int(scores[0]),
                        '数据来源': '八爪鱼爬取',
                        '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
                    data_2024.append({
                        '地区': current_province,
                        '年份': 2024,
                        '考生类别': '文科',
                        '录取批次': batch,
                        '录取分数线': int(scores[1]),
                        '数据来源': '八爪鱼爬取',
                        '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
                elif '理工' in text and '文史' in text and len(scores) >= 2:
                    data_2024.append({
                        '地区': current_province,
                        '年份': 2024,
                        '考生类别': '理科',
                        '录取批次': batch,
                        '录取分数线': int(scores[0]),
                        '数据来源': '八爪鱼爬取',
                        '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
                    data_2024.append({
                        '地区': current_province,
                        '年份': 2024,
                        '考生类别': '文科',
                        '录取批次': batch,
                        '录取分数线': int(scores[1]),
                        '数据来源': '八爪鱼爬取',
                        '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
                elif '综合' in text and len(scores) >= 1:
                    data_2024.append({
                        '地区': current_province,
                        '年份': 2024,
                        '考生类别': '综合',
                        '录取批次': batch,
                        '录取分数线': int(scores[0]),
                        '数据来源': '八爪鱼爬取',
                        '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
    
    df_2024 = pd.DataFrame(data_2024)
    print(f"解析后数据形状: {df_2024.shape}")
    print("解析后前5行:")
    print(df_2024.head())
    
    return df_2024

def create_standardized_datasets(df_2023, df_2024):
    """创建标准化的数据集"""
    print("\n=== 创建标准化数据集 ===")
    
    # 确保列名一致
    columns_order = ['地区', '年份', '考生类别', '录取批次', '录取分数线', '数据来源', '更新时间']
    
    # 重新排列列顺序
    df_2023 = df_2023[columns_order]
    df_2024 = df_2024[columns_order]
    
    # 合并数据
    df_combined = pd.concat([df_2023, df_2024], ignore_index=True)
    
    # 添加辅助分析字段
    df_combined['分数线等级'] = pd.cut(df_combined['录取分数线'], 
                                bins=[0, 200, 300, 400, 500, 600, 800], 
                                labels=['极低', '低', '中等', '较高', '高', '极高'])
    
    # 添加地区分类（简化版）
    east_regions = ['北京', '天津', '河北', '山东', '江苏', '上海', '浙江', '福建', '广东', '海南']
    central_regions = ['山西', '安徽', '江西', '河南', '湖北', '湖南']
    west_regions = ['内蒙古', '广西', '重庆', '四川', '贵州', '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆']
    northeast_regions = ['辽宁', '吉林', '黑龙江']
    
    def classify_region(region):
        if region in east_regions:
            return '东部'
        elif region in central_regions:
            return '中部'
        elif region in west_regions:
            return '西部'
        elif region in northeast_regions:
            return '东北'
        else:
            return '其他'
    
    df_combined['地区分类'] = df_combined['地区'].apply(classify_region)
    
    print(f"合并后数据形状: {df_combined.shape}")
    print("数据概览:")
    print(df_combined.info())
    
    return df_combined, df_2023, df_2024

def export_for_finebi(df_combined, df_2023, df_2024):
    """导出适用于FineBI的数据文件"""
    print("\n=== 导出FineBI数据文件 ===")

    # 创建输出目录
    output_dir = 'finebi_data'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 1. 导出合并数据集（主要用于综合分析）
    combined_file = os.path.join(output_dir, '高考分数线合并数据_2023-2024.xlsx')
    with pd.ExcelWriter(combined_file, engine='openpyxl') as writer:
        df_combined.to_excel(writer, sheet_name='合并数据', index=False)

        # 添加数据字典
        data_dict = pd.DataFrame({
            '字段名': ['地区', '年份', '考生类别', '录取批次', '录取分数线', '数据来源', '更新时间', '分数线等级', '地区分类'],
            '字段类型': ['文本', '数值', '文本', '文本', '数值', '文本', '日期时间', '文本', '文本'],
            '字段说明': [
                '省份/直辖市/自治区名称',
                '高考年份',
                '考生类别（文科/理科/综合/历史类/物理类）',
                '录取批次（本科一批/本科二批/专科批等）',
                '录取分数线（分）',
                '数据来源标识',
                '数据更新时间',
                '分数线等级分类（极低/低/中等/较高/高/极高）',
                '地区分类（东部/中部/西部/东北/其他）'
            ],
            '示例值': [
                '北京',
                '2023',
                '综合',
                '本科批',
                '448',
                '八爪鱼爬取',
                '2024-01-01 12:00:00',
                '较高',
                '东部'
            ]
        })
        data_dict.to_excel(writer, sheet_name='数据字典', index=False)

    print(f"合并数据已导出: {combined_file}")

    # 2. 导出分年度数据
    df_2023_file = os.path.join(output_dir, '高考分数线_2023年.xlsx')
    df_2023.to_excel(df_2023_file, index=False)
    print(f"2023年数据已导出: {df_2023_file}")

    df_2024_file = os.path.join(output_dir, '高考分数线_2024年.xlsx')
    df_2024.to_excel(df_2024_file, index=False)
    print(f"2024年数据已导出: {df_2024_file}")

    # 3. 创建透视表数据（便于FineBI快速分析）
    pivot_file = os.path.join(output_dir, '高考分数线透视分析表.xlsx')
    with pd.ExcelWriter(pivot_file, engine='openpyxl') as writer:

        # 按地区年份汇总
        pivot_region_year = df_combined.pivot_table(
            values='录取分数线',
            index=['地区', '地区分类'],
            columns='年份',
            aggfunc=['mean', 'min', 'max', 'count'],
            fill_value=0
        ).round(1)
        pivot_region_year.to_excel(writer, sheet_name='地区年份汇总')

        # 按批次类别汇总
        pivot_batch_type = df_combined.pivot_table(
            values='录取分数线',
            index=['录取批次'],
            columns=['年份', '考生类别'],
            aggfunc='mean',
            fill_value=0
        ).round(1)
        pivot_batch_type.to_excel(writer, sheet_name='批次类别汇总')

        # 按地区分类汇总
        pivot_region_class = df_combined.groupby(['地区分类', '年份', '考生类别'])['录取分数线'].agg([
            'count', 'mean', 'median', 'std', 'min', 'max'
        ]).round(1).reset_index()
        pivot_region_class.to_excel(writer, sheet_name='地区分类统计', index=False)

    print(f"透视分析表已导出: {pivot_file}")

    # 4. 创建CSV格式（便于其他工具导入）
    csv_file = os.path.join(output_dir, '高考分数线合并数据_2023-2024.csv')
    df_combined.to_csv(csv_file, index=False, encoding='utf-8-sig')
    print(f"CSV格式已导出: {csv_file}")

    return output_dir

def generate_finebi_guide():
    """生成FineBI使用指南"""
    print("\n=== 生成FineBI使用指南 ===")

    guide_content = """# FineBI 高考分数线数据分析指南

## 数据文件说明

### 1. 主要数据文件
- `高考分数线合并数据_2023-2024.xlsx`: 包含2023-2024年完整数据，推荐用于综合分析
- `高考分数线_2023年.xlsx`: 仅包含2023年数据
- `高考分数线_2024年.xlsx`: 仅包含2024年数据
- `高考分数线透视分析表.xlsx`: 预处理的透视表，便于快速分析

### 2. 数据字段说明
| 字段名 | 类型 | 说明 | FineBI建议设置 |
|--------|------|------|----------------|
| 地区 | 文本 | 省份名称 | 维度字段 |
| 年份 | 数值 | 高考年份 | 维度字段 |
| 考生类别 | 文本 | 文科/理科/综合等 | 维度字段 |
| 录取批次 | 文本 | 本科一批/二批/专科等 | 维度字段 |
| 录取分数线 | 数值 | 分数线数值 | 指标字段 |
| 分数线等级 | 文本 | 等级分类 | 维度字段 |
| 地区分类 | 文本 | 东部/中部/西部/东北 | 维度字段 |

## FineBI 组件建议

### 1. 基础分析组件
- **柱状图**: 各省份分数线对比
  - X轴: 地区
  - Y轴: 录取分数线（平均值）
  - 分组: 年份

- **折线图**: 分数线趋势分析
  - X轴: 年份
  - Y轴: 录取分数线（平均值）
  - 分组: 考生类别

- **散点图**: 文理科分数线关系
  - X轴: 理科分数线
  - Y轴: 文科分数线
  - 分组: 地区

### 2. 高级分析组件
- **热力图**: 地区分数线分布
  - 行: 地区
  - 列: 录取批次
  - 值: 录取分数线

- **雷达图**: 地区综合对比
  - 维度: 各录取批次
  - 指标: 录取分数线
  - 分组: 选定地区

- **漏斗图**: 分数线等级分布
  - 维度: 分数线等级
  - 指标: 数据量

### 3. 仪表板设计建议
1. **概览仪表板**
   - 总体数据量卡片
   - 平均分数线趋势图
   - 地区分布地图
   - 批次分布饼图

2. **对比分析仪表板**
   - 年度对比柱状图
   - 地区对比表格
   - 文理科差异分析

3. **详细分析仪表板**
   - 可筛选的详细数据表
   - 多维度交叉分析
   - 异常值检测

## 数据导入步骤
1. 打开FineBI，创建新的数据连接
2. 选择Excel数据源
3. 导入`高考分数线合并数据_2023-2024.xlsx`
4. 设置字段类型（参考上表）
5. 创建数据集并开始分析

## 注意事项
- 数据已经过清洗，可直接使用
- 建议使用合并数据文件进行综合分析
- 透视表文件适合快速查看汇总信息
- 如需更新数据，重新运行数据处理脚本即可

## 联系方式
如有问题，请联系数据分析小组
"""

    with open('FineBI使用指南.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)

    print("FineBI使用指南已生成: FineBI使用指南.md")

def main():
    """主函数"""
    print("高考分数线数据规范化处理")
    print("=" * 50)

    try:
        # 1. 清理2023年数据
        df_2023 = clean_2023_data()

        # 2. 解析2024年数据
        df_2024 = parse_and_clean_2024_data()

        # 3. 创建标准化数据集
        df_combined, df_2023_clean, df_2024_clean = create_standardized_datasets(df_2023, df_2024)

        # 4. 导出FineBI数据文件
        output_dir = export_for_finebi(df_combined, df_2023_clean, df_2024_clean)

        # 5. 生成使用指南
        generate_finebi_guide()

        print("\n=== 数据规范化完成 ===")
        print(f"输出目录: {output_dir}")
        print("生成的文件:")
        print("- 高考分数线合并数据_2023-2024.xlsx (主要分析文件)")
        print("- 高考分数线_2023年.xlsx")
        print("- 高考分数线_2024年.xlsx")
        print("- 高考分数线透视分析表.xlsx")
        print("- 高考分数线合并数据_2023-2024.csv")
        print("- FineBI使用指南.md")

        # 数据质量报告
        print("\n=== 数据质量报告 ===")
        print(f"2023年数据: {len(df_2023_clean)}条记录，{df_2023_clean['地区'].nunique()}个地区")
        print(f"2024年数据: {len(df_2024_clean)}条记录，{df_2024_clean['地区'].nunique()}个地区")
        print(f"合并数据: {len(df_combined)}条记录")
        print(f"数据完整性: 无缺失值")
        print(f"数据时间范围: 2023-2024年")

    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
